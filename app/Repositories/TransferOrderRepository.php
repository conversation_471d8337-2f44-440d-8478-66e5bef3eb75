<?php

namespace App\Repositories;

use App\Contracts\TransferOrderInterface;
use App\Models\TransferOrder;
use App\Models\TransferOrderItem;
use App\Models\TransferOrderImei;
use App\Models\TransferOrderBatch;
use App\Models\TransferRequest;
use App\Models\Warehouse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TransferOrderRepository extends BaseRepository implements TransferOrderInterface
{
    /**
     * TransferOrderRepository constructor.
     */
    public function __construct(TransferOrder $model)
    {
        parent::__construct($model);
    }

    /**
     * Get all transfer orders with pagination
     */
    public function getAllWithPagination($perPage = 15)
    {
        return $this->model->with(['transferRequest', 'fromWarehouse', 'toWarehouse', 'transitWarehouse', 'createdBy', 'approvedBy'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    /**
     * Get transfer order by ID
     */
    public function getById($id)
    {
        return $this->model->with([
            'transferRequest',
            'fromWarehouse',
            'toWarehouse',
            'transitWarehouse',
            'createdBy',
            'approvedBy',
            'items.product',
            'items.imeis',
            'items.batches'
        ])->findOrFail($id);
    }

    /**
     * Create new transfer order
     */
    public function create(array $data)
    {
        try {
            DB::beginTransaction();

            // Generate code if not provided
            if (!isset($data['code'])) {
                $data['code'] = TransferOrder::generateCode();
            }

            // Get transit warehouse if not provided
            if (!isset($data['transit_warehouse_id'])) {
                $transitWarehouse = Warehouse::where('is_transit', true)->first();
                if ($transitWarehouse) {
                    $data['transit_warehouse_id'] = $transitWarehouse->id;
                }
            }

            $transferOrder = $this->model->create($data);

            // Create items if provided
            if (isset($data['items']) && is_array($data['items'])) {
                $this->createItems($transferOrder->id, $data['items']);
            }

            DB::commit();
            return $transferOrder;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating transfer order: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update transfer order
     */
    public function update($id, array $data)
    {
        try {
            DB::beginTransaction();

            $transferOrder = $this->getById($id);
            $transferOrder->update($data);

            // Update items if provided
            if (isset($data['items']) && is_array($data['items'])) {
                $this->updateItems($transferOrder->id, $data['items']);
            }

            DB::commit();
            return $transferOrder;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating transfer order: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete transfer order
     */
    public function delete($id)
    {
        try {
            DB::beginTransaction();

            $transferOrder = $this->getById($id);

            // Delete items first
            $transferOrder->items()->delete();

            // Delete transfer order
            $transferOrder->delete();

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting transfer order: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get transfer orders for datatable
     */
    public function getForDatatable(array $params)
    {
        try {
            $query = DB::table('transfer_orders as to')
                ->leftJoin('transfer_requests as tr', 'to.transfer_request_id', '=', 'tr.id')
                ->leftJoin('warehouses as wf', 'to.from_warehouse_id', '=', 'wf.id')
                ->leftJoin('warehouses as wt', 'to.to_warehouse_id', '=', 'wt.id')
                ->leftJoin('warehouses as wtr', 'to.transit_warehouse_id', '=', 'wtr.id')
                ->leftJoin('users as uc', 'to.created_by', '=', 'uc.id')
                ->leftJoin('users as ua', 'to.approved_by', '=', 'ua.id')
                ->select(
                    'to.id',
                    'to.code',
                    'to.status',
                    'to.notes',
                    'to.created_at',
                    'to.approved_at',
                    'tr.code as transfer_request_code',
                    'wf.name as from_warehouse_name',
                    'wt.name as to_warehouse_name',
                    'wtr.name as transit_warehouse_name',
                    'uc.name as created_by_name',
                    'ua.name as approved_by_name'
                )
                ->whereNull('to.deleted_at');

            // Apply filters
            if (isset($params['status']) && !empty($params['status'])) {
                $query->where('to.status', $params['status']);
            }

            if (isset($params['from_warehouse_id']) && !empty($params['from_warehouse_id'])) {
                $query->where('to.from_warehouse_id', $params['from_warehouse_id']);
            }

            if (isset($params['to_warehouse_id']) && !empty($params['to_warehouse_id'])) {
                $query->where('to.to_warehouse_id', $params['to_warehouse_id']);
            }

            if (isset($params['search']['value']) && !empty($params['search']['value'])) {
                $searchValue = $params['search']['value'];
                $query->where(function ($q) use ($searchValue) {
                    $q->where('to.code', 'like', "%{$searchValue}%")
                      ->orWhere('tr.code', 'like', "%{$searchValue}%")
                      ->orWhere('wf.name', 'like', "%{$searchValue}%")
                      ->orWhere('wt.name', 'like', "%{$searchValue}%")
                      ->orWhere('uc.name', 'like', "%{$searchValue}%");
                });
            }

            // Get total count before pagination
            $totalRecords = $query->count();

            // Apply ordering
            if (isset($params['order'][0]['column']) && isset($params['columns'][$params['order'][0]['column']]['data'])) {
                $orderColumn = $params['columns'][$params['order'][0]['column']]['data'];
                $orderDirection = $params['order'][0]['dir'] ?? 'asc';

                $orderColumnMap = [
                    'code' => 'to.code',
                    'transfer_request_code' => 'tr.code',
                    'from_warehouse' => 'wf.name',
                    'to_warehouse' => 'wt.name',
                    'status' => 'to.status',
                    'created_at' => 'to.created_at',
                    'created_by' => 'uc.name'
                ];

                if (isset($orderColumnMap[$orderColumn])) {
                    $query->orderBy($orderColumnMap[$orderColumn], $orderDirection);
                }
            } else {
                $query->orderBy('to.created_at', 'desc');
            }

            // Apply pagination
            if (isset($params['start']) && isset($params['length'])) {
                $query->offset($params['start'])->limit($params['length']);
            }

            $data = $query->get();

            return [
                'data' => $data,
                'recordsTotal' => $totalRecords,
                'recordsFiltered' => $totalRecords
            ];
        } catch (\Exception $e) {
            Log::error('Error getting transfer orders for datatable: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Submit transfer order for approval
     */
    public function submitForApproval($id)
    {
        $transferOrder = $this->getById($id);

        if (!$transferOrder->canBeSubmitted()) {
            throw new \Exception('Phiếu chuyển hàng không thể gửi duyệt.');
        }

        $transferOrder->update(['status' => TransferOrder::STATUS_PENDING]);
        return $transferOrder;
    }

    /**
     * Approve transfer order
     */
    public function approve($id, $approvedBy)
    {
        try {
            DB::beginTransaction();

            $transferOrder = $this->getById($id);

            if (!$transferOrder->canBeApproved()) {
                throw new \Exception('Phiếu chuyển hàng không thể duyệt.');
            }

            // Update transfer order status
            $transferOrder->update([
                'status' => TransferOrder::STATUS_APPROVED,
                'approved_by' => $approvedBy,
                'approved_at' => now()
            ]);

            // Process inventory transactions
            $this->processInventoryTransactions($transferOrder);

            DB::commit();
            return $transferOrder;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error approving transfer order: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Process inventory transactions when transfer order is approved
     */
    private function processInventoryTransactions($transferOrder)
    {
        // This will be implemented to handle inventory movements
        // For now, we'll just log the action
        Log::info('Processing inventory transactions for transfer order: ' . $transferOrder->code);

        // TODO: Implement inventory transaction logic
        // - Decrease inventory in source warehouse
        // - Increase inventory in transit warehouse
    }

    /**
     * Reject transfer order
     */
    public function reject($id, $rejectedBy, $reason = null)
    {
        $transferOrder = $this->getById($id);

        if (!$transferOrder->canBeRejected()) {
            throw new \Exception('Phiếu chuyển hàng không thể từ chối.');
        }

        $updateData = [
            'status' => TransferOrder::STATUS_REJECTED,
            'approved_by' => $rejectedBy,
            'approved_at' => now()
        ];

        if ($reason) {
            $updateData['notes'] = ($transferOrder->notes ? $transferOrder->notes . "\n\n" : '') . "Lý do từ chối: " . $reason;
        }

        $transferOrder->update($updateData);
        return $transferOrder;
    }

    /**
     * Cancel transfer order
     */
    public function cancel($id)
    {
        $transferOrder = $this->getById($id);

        if (!$transferOrder->canBeCancelled()) {
            throw new \Exception('Phiếu chuyển hàng không thể hủy.');
        }

        $transferOrder->update(['status' => TransferOrder::STATUS_CANCELLED]);
        return $transferOrder;
    }

    /**
     * Get approved transfer orders that don't have transfer receipts
     */
    public function getApprovedWithoutTransferReceipt()
    {
        return $this->model->with(['fromWarehouse', 'toWarehouse', 'transitWarehouse'])
            ->where('status', TransferOrder::STATUS_APPROVED)
            ->whereDoesntHave('transferReceipt')
            ->orderBy('approved_at', 'asc')
            ->get();
    }

    /**
     * Create transfer order items
     */
    public function createItems($transferOrderId, array $items)
    {
        try {
            foreach ($items as $item) {
                $item['transfer_order_id'] = $transferOrderId;
                TransferOrderItem::create($item);
            }
            return true;
        } catch (\Exception $e) {
            Log::error('Error creating transfer order items: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update transfer order items
     */
    public function updateItems($transferOrderId, array $items)
    {
        try {
            // Delete existing items
            TransferOrderItem::where('transfer_order_id', $transferOrderId)->delete();

            // Create new items
            $this->createItems($transferOrderId, $items);

            return true;
        } catch (\Exception $e) {
            Log::error('Error updating transfer order items: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete transfer order items
     */
    public function deleteItems($transferOrderId, array $itemIds = [])
    {
        try {
            $query = TransferOrderItem::where('transfer_order_id', $transferOrderId);

            if (!empty($itemIds)) {
                $query->whereIn('id', $itemIds);
            }

            $query->delete();
            return true;
        } catch (\Exception $e) {
            Log::error('Error deleting transfer order items: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Create transfer order from transfer request
     */
    public function createFromTransferRequest($transferRequestId, array $data)
    {
        try {
            DB::beginTransaction();

            $transferRequest = TransferRequest::with('items')->findOrFail($transferRequestId);

            // Prepare transfer order data
            $transferOrderData = [
                'transfer_request_id' => $transferRequestId,
                'from_warehouse_id' => $transferRequest->from_warehouse_id,
                'to_warehouse_id' => $transferRequest->to_warehouse_id,
                'notes' => $data['notes'] ?? $transferRequest->notes,
                'created_by' => $data['created_by'],
            ];

            // Get transit warehouse
            $transitWarehouse = Warehouse::where('is_transit', true)->first();
            if ($transitWarehouse) {
                $transferOrderData['transit_warehouse_id'] = $transitWarehouse->id;
            }

            $transferOrder = $this->create($transferOrderData);

            // Copy items from transfer request
            $items = [];
            foreach ($transferRequest->items as $requestItem) {
                $items[] = [
                    'product_id' => $requestItem->product_id,
                    'quantity' => $requestItem->quantity,
                    'notes' => $requestItem->notes,
                ];
            }

            if (!empty($items)) {
                $this->createItems($transferOrder->id, $items);
            }

            DB::commit();
            return $transferOrder;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating transfer order from transfer request: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Validate IMEI availability for transfer order
     */
    public function validateImeiAvailability($transferOrderId, $itemId, $imei)
    {
        $transferOrder = $this->getById($transferOrderId);
        $item = $transferOrder->items()->findOrFail($itemId);

        if ($item->product->inventory_tracking_type !== 'serial') {
            throw new \Exception('Sản phẩm này không quản lý bằng IMEI.');
        }

        // Check if IMEI already exists in this transfer order
        $existingImei = $item->imeis()->where('imei', $imei)->first();
        if ($existingImei) {
            throw new \Exception('IMEI này đã được thêm vào phiếu chuyển hàng.');
        }

        // Check if IMEI exists in system and belongs to source warehouse
        $productSerial = \App\Models\ProductSerial::where('serial_number', $imei)
            ->where('product_id', $item->product_id)
            ->where('warehouse_id', $transferOrder->from_warehouse_id)
            ->where('status', 'in_stock')
            ->first();

        if (!$productSerial) {
            throw new \Exception('IMEI không tồn tại hoặc không thuộc kho nguồn hoặc đã được sử dụng.');
        }

        return [
            'valid' => true,
            'imei' => $imei,
            'product_serial' => $productSerial
        ];
    }

    /**
     * Add IMEI to transfer order item
     */
    public function addImei($transferOrderId, $itemId, $imei, $scannedBy)
    {
        try {
            // Validate IMEI availability first
            $this->validateImeiAvailability($transferOrderId, $itemId, $imei);

            $transferOrder = $this->getById($transferOrderId);

            if ($transferOrder->status !== TransferOrder::STATUS_DRAFT) {
                throw new \Exception('Chỉ có thể thêm IMEI khi phiếu chuyển hàng ở trạng thái nháp.');
            }

            $item = $transferOrder->items()->findOrFail($itemId);

            // Check if we've reached the quantity limit
            if ($item->scanned_imei_count >= $item->quantity) {
                throw new \Exception('Đã đủ số lượng IMEI cho sản phẩm này.');
            }

            $transferOrderImei = TransferOrderImei::create([
                'transfer_order_item_id' => $itemId,
                'imei' => $imei,
                'scanned_by' => $scannedBy,
                'scanned_at' => now()
            ]);

            return $transferOrderImei;
        } catch (\Exception $e) {
            Log::error('Error adding IMEI to transfer order: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Remove IMEI from transfer order item
     */
    public function removeImei($transferOrderId, $imeiId)
    {
        try {
            $transferOrder = $this->getById($transferOrderId);

            if ($transferOrder->status !== TransferOrder::STATUS_DRAFT) {
                throw new \Exception('Chỉ có thể xóa IMEI khi phiếu chuyển hàng ở trạng thái nháp.');
            }

            $imei = TransferOrderImei::findOrFail($imeiId);
            $imei->delete();

            return true;
        } catch (\Exception $e) {
            Log::error('Error removing IMEI from transfer order: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Validate batch availability for transfer order
     */
    public function validateBatchAvailability($transferOrderId, $itemId, $batchNumber, $quantity)
    {
        $transferOrder = $this->getById($transferOrderId);
        $item = $transferOrder->items()->findOrFail($itemId);

        if ($item->product->inventory_tracking_type !== 'batch') {
            throw new \Exception('Sản phẩm này không quản lý bằng batch.');
        }

        // Check if batch already exists in this transfer order
        $existingBatch = $item->batches()->where('batch_number', $batchNumber)->first();
        if ($existingBatch) {
            throw new \Exception('Batch này đã được thêm vào phiếu chuyển hàng.');
        }

        // Check if total batch quantity would exceed item quantity
        $currentTotal = $item->total_batch_quantity;
        if ($currentTotal + $quantity > $item->quantity) {
            throw new \Exception('Tổng số lượng batch vượt quá số lượng sản phẩm.');
        }

        // Check if batch exists in system and has enough quantity in source warehouse
        $productBatch = \App\Models\ProductBatch::where('batch_number', $batchNumber)
            ->where('product_id', $item->product_id)
            ->where('warehouse_id', $transferOrder->from_warehouse_id)
            ->where('status', 'active')
            ->first();

        if (!$productBatch) {
            throw new \Exception('Batch không tồn tại hoặc không thuộc kho nguồn.');
        }

        // Check available quantity in source warehouse
        $availableQuantity = $productBatch->quantity ?? 0;
        if ($availableQuantity < $quantity) {
            throw new \Exception("Batch chỉ còn {$availableQuantity} trong kho nguồn, không đủ để chuyển {$quantity}.");
        }

        return [
            'valid' => true,
            'batch_number' => $batchNumber,
            'quantity' => $quantity,
            'available_quantity' => $availableQuantity,
            'product_batch' => $productBatch
        ];
    }

    /**
     * Add batch to transfer order item
     */
    public function addBatch($transferOrderId, $itemId, array $batchData)
    {
        try {
            // Validate batch availability first
            $this->validateBatchAvailability($transferOrderId, $itemId, $batchData['batch_number'], $batchData['quantity']);

            $transferOrder = $this->getById($transferOrderId);

            if ($transferOrder->status !== TransferOrder::STATUS_DRAFT) {
                throw new \Exception('Chỉ có thể thêm batch khi phiếu chuyển hàng ở trạng thái nháp.');
            }

            $item = $transferOrder->items()->findOrFail($itemId);

            $transferOrderBatch = TransferOrderBatch::create([
                'transfer_order_item_id' => $itemId,
                'batch_number' => $batchData['batch_number'],
                'quantity' => $batchData['quantity'],
                'expiry_date' => $batchData['expiry_date'] ?? null
            ]);

            return $transferOrderBatch;
        } catch (\Exception $e) {
            Log::error('Error adding batch to transfer order: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Remove batch from transfer order item
     */
    public function removeBatch($transferOrderId, $batchId)
    {
        try {
            $transferOrder = $this->getById($transferOrderId);

            if ($transferOrder->status !== TransferOrder::STATUS_DRAFT) {
                throw new \Exception('Chỉ có thể xóa batch khi phiếu chuyển hàng ở trạng thái nháp.');
            }

            $batch = TransferOrderBatch::findOrFail($batchId);
            $batch->delete();

            return true;
        } catch (\Exception $e) {
            Log::error('Error removing batch from transfer order: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Check if transfer order is ready for approval (all IMEI/batch items are prepared)
     */
    public function isReadyForApproval($transferOrderId)
    {
        $transferOrder = $this->getById($transferOrderId);

        foreach ($transferOrder->items as $item) {
            if (!$item->is_fully_prepared) {
                return false;
            }
        }

        return true;
    }
}
