<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class TransferPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [
            // Transfer Request permissions
            'transfer-requests.list' => 'Xem danh sách yêu cầu chuyển kho',
            'transfer-requests.create' => 'Tạo yêu cầu chuyển kho',
            'transfer-requests.view' => 'Xem chi tiết yêu cầu chuyển kho',
            'transfer-requests.edit' => 'Chỉnh sửa yêu cầu chuyển kho',
            'transfer-requests.delete' => 'Xóa yêu cầu chuyển kho',
            'transfer-requests.submit' => 'Gửi duyệt yêu cầu chuyển kho',
            'transfer-requests.approve' => 'Duyệt yêu cầu chuyển kho',
            'transfer-requests.reject' => 'Từ chối yêu cầu chuyển kho',
            'transfer-requests.cancel' => 'Hủy yêu cầu chuyển kho',

            // Transfer Order permissions (Auto-generated only)
            'transfer-orders.list' => 'Xem danh sách phiếu chuyển hàng',
            'transfer-orders.create' => 'Tạo phiếu chuyển hàng từ yêu cầu chuyển kho',
            'transfer-orders.view' => 'Xem chi tiết phiếu chuyển hàng',
            // 'transfer-orders.edit' => 'Chỉnh sửa phiếu chuyển hàng', // DISABLED: Manual editing not allowed
            'transfer-orders.delete' => 'Xóa phiếu chuyển hàng',
            'transfer-orders.submit' => 'Gửi duyệt phiếu chuyển hàng',
            'transfer-orders.approve' => 'Duyệt phiếu chuyển hàng',
            'transfer-orders.reject' => 'Từ chối phiếu chuyển hàng',
            'transfer-orders.cancel' => 'Hủy phiếu chuyển hàng',

            // Transfer Receipt permissions (Auto-generated only)
            'transfer-receipts.list' => 'Xem danh sách phiếu nhận hàng',
            // 'transfer-receipts.create' => 'Tạo phiếu nhận hàng', // DISABLED: Manual creation not allowed
            'transfer-receipts.create-from-order' => 'Tạo phiếu nhận hàng từ phiếu chuyển hàng',
            'transfer-receipts.view' => 'Xem chi tiết phiếu nhận hàng',
            // 'transfer-receipts.edit' => 'Chỉnh sửa phiếu nhận hàng', // DISABLED: Manual editing not allowed
            'transfer-receipts.delete' => 'Xóa phiếu nhận hàng',
            'transfer-receipts.submit' => 'Gửi duyệt phiếu nhận hàng',
            'transfer-receipts.approve' => 'Duyệt phiếu nhận hàng',
            'transfer-receipts.reject' => 'Từ chối phiếu nhận hàng',
            'transfer-receipts.cancel' => 'Hủy phiếu nhận hàng',

            // General transfer permissions
            'transfers.view' => 'Xem chức năng chuyển kho',
            'transfers.manage' => 'Quản lý chuyển kho',
        ];

        foreach ($permissions as $permission => $description) {
            Permission::firstOrCreate(['name' => $permission], [
                'guard_name' => 'web'
            ]);
        }

        // Assign permissions to super admin role
        $superAdminRole = Role::where('name', 'super-admin')->first();
        if ($superAdminRole) {
            $superAdminRole->givePermissionTo(array_keys($permissions));
        }

        // Create warehouse manager role with transfer permissions
        $warehouseManagerRole = Role::firstOrCreate(['name' => 'warehouse-manager'], [
            'guard_name' => 'web'
        ]);

        $warehouseManagerPermissions = [
            'transfer-requests.list',
            'transfer-requests.create',
            'transfer-requests.view',
            'transfer-requests.edit',
            'transfer-requests.submit',
            'transfer-orders.list',
            'transfer-orders.create',
            'transfer-orders.view',
            // 'transfer-orders.edit', // DISABLED: Manual editing not allowed
            'transfer-orders.submit',
            'transfer-receipts.list',
            // 'transfer-receipts.create', // DISABLED: Manual creation not allowed
            'transfer-receipts.create-from-order',
            'transfer-receipts.view',
            // 'transfer-receipts.edit', // DISABLED: Manual editing not allowed
            'transfer-receipts.submit',
            'transfers.view',
        ];

        $warehouseManagerRole->givePermissionTo($warehouseManagerPermissions);

        // Create warehouse supervisor role with approval permissions
        $warehouseSupervisorRole = Role::firstOrCreate(['name' => 'warehouse-supervisor'], [
            'guard_name' => 'web'
        ]);

        $warehouseSupervisorPermissions = array_merge($warehouseManagerPermissions, [
            'transfer-requests.approve',
            'transfer-requests.reject',
            'transfer-requests.cancel',
            'transfer-orders.approve',
            'transfer-orders.reject',
            'transfer-orders.cancel',
            'transfer-receipts.approve',
            'transfer-receipts.reject',
            'transfer-receipts.cancel',
            'transfers.manage',
        ]);

        $warehouseSupervisorRole->givePermissionTo($warehouseSupervisorPermissions);
    }
}
